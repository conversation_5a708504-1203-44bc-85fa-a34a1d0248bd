<script setup lang="ts">
// 住宿详单组件
import { message } from 'ant-design-vue';
import { onMounted, ref, defineProps, defineEmits, watch, nextTick, computed } from 'vue';
import { fileApi } from '@haierbusiness-front/apis';
import arrow from '@/assets/image/orderList/delete.png'; //删除
import add from '@/assets/image/orderList/add.png'; //添加
import { UploadFile } from '@haierbusiness-front/common-libs';
import { UploadOutlined } from '@ant-design/icons-vue';

// 类型定义
interface AccommodationDetailItem {
  tempId: string; // 临时ID
  serialNumber: number; // 序号
  checkInPersonNum: number | null; // 签到人数
  detailPersonNum: number | null; // 详单人数
  comparisonResult: number; // 比对结果 (0:不一致,1:一致)
  paths: string[]; // 附件路径数组
  // 内部使用但不传递给父组件
  _attachments?: UploadFile[]; // 附件文件列表（仅用于界面显示）
  _uploading?: boolean; // 当前条目是否正在上传
}


// 文件上传相关常量
const SUPPORTED_FILE_TYPES = ['application/pdf', 'image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
const FILE_SIZE_LIMIT = 10; // MB
const UPLOAD_ACCEPT = '.pdf,.jpg,.jpeg,.png,.gif,.doc,.docx';

const props = defineProps({
  accommodationDetailList: {
    type: Array as () => AccommodationDetailItem[],
    default: () => [],
  },
  // 实际签到人数，来自父组件
  realSignInPersonNum: {
    type: Number,
    default: 0,
  },
  readonly: {
    type: Boolean,
    default: false,
  },
  // 🔥 新增：查看模式下的方案详情数据
  schemeDetail: {
    type: Object as () => any,
    default: () => ({}),
  },
});

const emit = defineEmits(['accommodationDetailEmit']);

// 响应式数据
const previewVisible = ref(false);
const previewFile = ref<UploadFile | null>(null);
const previewFileName = ref('');

// 用于存储每个条目的上传状态
const itemUploadingState = ref<Record<string, boolean>>({});


// 获取基础URL
const baseUrl = import.meta.env.VITE_BUSINESS_URL;

// 初始化标志，防止重复初始化
const isInitialized = ref(false);

// 🔥 计算属性：获取要显示的住宿详单数据
const displayAccommodationDetailList = computed(() => {
  if (props.readonly && props.schemeDetail?.attachmentStayChecks) {
    // 查看模式：从 schemeDetail.attachmentStayChecks 获取数据

    const transformedData = props.schemeDetail.attachmentStayChecks.map((item: any, index: number) => {
      // 🔥 将 paths 转换为 _attachments 格式
      const _attachments = (item.paths || []).map((path: string, pathIndex: number) => {
        // 处理路径，确保正确的 URL 格式
        let processedPath = path;

        // 如果路径已经包含完整 URL，直接使用
        if (path.startsWith('http')) {
          processedPath = path;
        } else {
          // 如果是相对路径，拼接 baseUrl
          processedPath = path.startsWith('/') ? baseUrl + path : baseUrl + '/' + path;
        }

        // 生成文件名
        const fileName = path.split('/').pop() || `住宿详单${pathIndex + 1}`;

        return {
          uid: `${item.id || index}_${pathIndex}`,
          name: fileName,
          status: 'done',
          url: processedPath,
          filePath: processedPath,
        };
      });

      return {
        ...item,
        tempId: item.tempId || `view_accommodation_${item.id || Date.now()}_${index}`,
        serialNumber: item.serialNumber || (index + 1),
        _attachments: _attachments,
      };
    });

    return transformedData;
  } else {
    // 编辑模式：使用 accommodationDetailList
    return props.accommodationDetailList || [];
  }
});

// 从缓存数据初始化住宿详单
const initAccommodationDetailFromCache = (cacheData: AccommodationDetailItem[]) => {
  if (!cacheData || cacheData.length === 0) {
    return;
  }

  // 处理缓存数据，将 paths 转换为 _attachments
  const processedData = cacheData.map((item: AccommodationDetailItem, index: number) => {
    const processedItem = { ...item };

    // 处理附件数据 - 将字符串路径转换为 UploadFile 对象
    if (item.paths && Array.isArray(item.paths) && item.paths.length > 0) {
      processedItem._attachments = item.paths.map((path: string, fileIndex: number) => {
        // 处理路径，确保正确的 URL 格式
        let processedPath = path;

        // 如果路径已经包含完整 URL，提取相对路径
        if (path.includes(baseUrl)) {
          processedPath = path.replace(baseUrl, '');
        }

        // 确保路径以 / 开头
        if (!processedPath.startsWith('/')) {
          processedPath = '/' + processedPath;
        }

        return {
          uid: `${item.tempId || Date.now()}_cache_${fileIndex}`,
          name: path.split('/').pop() || `住宿详单${fileIndex + 1}`,
          status: 'done' as const,
          url: baseUrl + processedPath,
          filePath: processedPath,
          fileName: path.split('/').pop() || `住宿详单${fileIndex + 1}`,
        };
      });
    } else {
      processedItem._attachments = [];
    }


    return processedItem;
  });

  isInitialized.value = true;

  // 发射处理后的数据到父组件
  emit('accommodationDetailEmit', processedData);
};

// 获取文件显示名称
const getFileDisplayName = (fileName: string): string => {
  if (!fileName) return '未知文件';

  const maxLength = 15;
  if (fileName.length <= maxLength) return fileName;

  const extension = fileName.split('.').pop() || '';
  const nameWithoutExt = fileName.substring(0, fileName.lastIndexOf('.'));
  const truncatedName = nameWithoutExt.substring(0, maxLength - extension.length - 4) + '...';

  return `${truncatedName}.${extension}`;
};

// 新增住宿详单 - 限制只能有一条
const handleAddAccommodationDetail = () => {
  // 检查是否已有数据，限制只能有一条
  if (props.accommodationDetailList.length >= 1) {
    message.warning('住宿详单最多只能添加一条记录');
    return;
  }

  const newItem: AccommodationDetailItem = {
    tempId: `${Date.now()}_${Math.random()}`,
    serialNumber: 1, // 固定为1，因为只能有一条
    checkInPersonNum: props.realSignInPersonNum, // 使用传入的实际签到人数
    detailPersonNum: null,
    comparisonResult: 0, // 默认为不一致(0)
    paths: [],
    _attachments: [],
    _uploading: false,
  };

  const updatedList = [...props.accommodationDetailList, newItem];
  emit('accommodationDetailEmit', updatedList);
};


// 更新住宿详单字段
const updateAccommodationDetail = (itemId: string, field: string, value: any) => {
  const updatedList = props.accommodationDetailList.map((item) => {
    if (item.tempId === itemId) {
      return { ...item, [field]: value };
    }
    return item;
  });
  emit('accommodationDetailEmit', updatedList);
};

// 检查两个人数是否一致并更新比对结果
const updateComparisonResult = (itemId: string) => {
  const updatedList = props.accommodationDetailList.map((item) => {
    if (item.tempId === itemId) {
      const isMatch =
        item.checkInPersonNum !== null &&
        item.detailPersonNum !== null &&
        item.checkInPersonNum === item.detailPersonNum;
      return { ...item, comparisonResult: isMatch ? 1 : 0 }; // 1: 一致, 0: 不一致
    }
    return item;
  });
  emit('accommodationDetailEmit', updatedList);
};


// 文件上传处理
const uploadRequest = (options: any, itemId: string) => {
  // 文件验证
  const file = options.file;
  const isValidType =
    SUPPORTED_FILE_TYPES.includes(file.type) ||
    file.name.toLowerCase().endsWith('.pdf') ||
    file.name.toLowerCase().endsWith('.doc') ||
    file.name.toLowerCase().endsWith('.docx');

  if (!isValidType) {
    message.error('只支持上传 PDF、图片、Word 文档格式的文件！');
    return;
  }

  const isValidSize = file.size / 1024 / 1024 < FILE_SIZE_LIMIT;
  if (!isValidSize) {
    message.error(`文件大小不能超过 ${FILE_SIZE_LIMIT}MB！`);
    return;
  }

  // 设置该条目的上传状态
  itemUploadingState.value[itemId] = true;

  // 更新条目的上传状态
  const updatedListForLoading = props.accommodationDetailList.map((item) => {
    if (item.tempId === itemId) {
      return { ...item, _uploading: true };
    }
    return item;
  });
  emit('accommodationDetailEmit', updatedListForLoading);

  const formData = new FormData();
  formData.append('file', file);

  // 创建一个临时文件对象用于立即回显
  const tempFileObj: UploadFile = {
    uid: file.uid || Date.now().toString(),
    name: file.name,
    status: 'uploading',
    fileName: file.name,
  };

  // 先更新UI，添加临时文件
  const updatedTempList = props.accommodationDetailList.map((item) => {
    if (item.tempId === itemId) {
      return {
        ...item,
        _attachments: [...(item._attachments || []), tempFileObj],
      };
    }
    return item;
  });
  emit('accommodationDetailEmit', updatedTempList);

  // 执行上传
  fileApi
    .upload(formData)
    .then((response) => {
      // 上传成功后更新文件对象
      const fileObj: UploadFile = {
        uid: tempFileObj.uid,
        name: file.name,
        status: 'done',
        url: response.path ? baseUrl + response.path : '',
        filePath: response.path ? response.path : '', // 存储相对路径
        fileName: file.name,
      };

      // 更新文件列表
      const updatedList = props.accommodationDetailList.map((item) => {
        if (item.tempId === itemId) {
          // 找到当前临时文件并替换它
          const updatedAttachments = (item._attachments || []).map((attachment) =>
            attachment.uid === tempFileObj.uid ? fileObj : attachment,
          );

          // 存储完整URL路径，而不仅仅是相对路径
          const fullPath = response.path
            ? response.path.startsWith('/')
              ? baseUrl + response.path
              : baseUrl + '/' + response.path
            : '';

          return {
            ...item,
            _attachments: updatedAttachments,
            paths: [...item.paths, fullPath],
          };
        }
        return item;
      });

      emit('accommodationDetailEmit', updatedList);
      message.success(`文件 ${file.name} 上传成功`);
    })
    .catch((error) => {
      message.error(`文件 ${file.name} 上传失败，请重试`);

      // 从列表中移除临时文件
      const failedList = props.accommodationDetailList.map((item) => {
        if (item.tempId === itemId) {
          return {
            ...item,
            _attachments: (item._attachments || []).filter((file) => file.uid !== tempFileObj.uid),
            _uploading: false,
          };
        }
        return item;
      });

      emit('accommodationDetailEmit', failedList);
    })
    .finally(() => {
      // 清除该条目的上传状态
      itemUploadingState.value[itemId] = false;

      // 更新条目状态
      const finalList = props.accommodationDetailList.map((item) => {
        if (item.tempId === itemId) {
          return { ...item, _uploading: false };
        }
        return item;
      });
      emit('accommodationDetailEmit', finalList);
    });
};

// 删除文件 - 真正删除数据而不是隐藏
const handleRemoveFile = (file: UploadFile, itemId: string) => {
  const updatedList = props.accommodationDetailList.map((item) => {
    if (item.tempId === itemId) {
      const index = item._attachments?.findIndex((f) => f.uid === file.uid) ?? -1;
      if (index > -1) {
        // 创建新的数组，真正删除对应索引的数据
        const newAttachments = [...(item._attachments || [])];
        const newPaths = [...item.paths];

        // 从数组中移除对应索引的元素
        newAttachments.splice(index, 1);
        newPaths.splice(index, 1);

        return {
          ...item,
          _attachments: newAttachments,
          paths: newPaths,
        };
      }
    }
    return item;
  });

  // 立即更新到父组件，确保数据真正被删除
  emit('accommodationDetailEmit', updatedList);
  message.success('文件删除成功');
};

// 文件预览
const handlePreviewFile = (file: UploadFile) => {
  previewFile.value = file;
  previewFileName.value = file.name;
  previewVisible.value = true;
};

// 通过路径预览文件
const handlePreviewPath = (path: string, itemId: string, index: number) => {
  if (!path) {
    message.warning('无法打开文件，文件路径不存在');
    return;
  }

  const item = props.accommodationDetailList.find((item) => item.tempId === itemId);
  if (item && item._attachments && item._attachments[index]) {
    handlePreviewFile(item._attachments[index]);
  } else {
    // 如果没有关联的附件对象，尝试打开URL
    const fullUrl = path.startsWith('/') ? baseUrl + path : baseUrl + '/' + path;
    window.open(fullUrl, '_blank');
  }
};

// 关闭预览
const handlePreviewCancel = () => {
  previewVisible.value = false;
  previewFile.value = null;
  previewFileName.value = '';
};

// 下载文件
const handleDownloadFile = () => {
  if (previewFile.value && previewFile.value.url) {
    window.open(previewFile.value.url, '_blank');
  }
};


// 获取提交数据 - 过滤掉_attachments字段
const getSubmitData = () => {
  return props.accommodationDetailList.map((item) => {
    const { _attachments, ...submitItem } = item;
    return {
      ...submitItem,
      checkInPersonNum: submitItem.checkInPersonNum || 0, // 确保不是null
      detailPersonNum: submitItem.detailPersonNum || 0, // 确保不是null
      comparisonResult: submitItem.comparisonResult, // 保持数字格式 (0:不一致,1:一致)
      paths: submitItem.paths || [], // 确保是数组
    };
  });
};

// 锚点跳转函数
const anchorJump = (id: string) => {
  const element = document.getElementById(id);
  if (element) {
    element.scrollIntoView({ behavior: 'smooth', block: 'center' });
  }
};

// 暂存方法
const accommodationDetailTempSave = () => {
  emit('accommodationDetailEmit', props.accommodationDetailList);
};

const accommodationDetailSub = () => {
  let isVerPassed = true;

  if (!props.accommodationDetailList || props.accommodationDetailList.length === 0) {
    message.error('请添加住宿详单！');
    anchorJump('accommodationTable')
    isVerPassed = false;
    return isVerPassed;
  }

  // 验证每个条目的必填字段
  for (const item of props.accommodationDetailList) {
    if (!item._attachments || item._attachments.length == 0) {
      message.error('请上传附件！');
      anchorJump('accommodation'+item.serialNumber)
      isVerPassed = false;
      break;
    }
    if (item.checkInPersonNum === null || item.checkInPersonNum === undefined) {
      message.error('签到人数不能为空！');
      anchorJump('accommodation'+item.serialNumber)
      isVerPassed = false;
      break;
    }
    if (item.detailPersonNum === null || item.detailPersonNum === undefined) {
      message.error('请输入详单人数！');
      anchorJump('accommodation'+item.serialNumber)
      isVerPassed = false;
      break;
    }
  }

  if (isVerPassed) {
    accommodationDetailTempSave();
  }

  return isVerPassed;
};

// 删除条目
const handleDeleteItem = (tempId: string) => {
  const index = props.accommodationDetailList.findIndex(item => item.tempId === tempId);
  if (index > -1) {
    const deletedItem = props.accommodationDetailList[index];
    const updatedList = [...props.accommodationDetailList];
    updatedList.splice(index, 1);

    // 重新排序序号
    updatedList.forEach((item, idx) => {
      item.serialNumber = idx + 1;
    });

    emit('accommodationDetailEmit', updatedList);
    message.success('条目删除成功');
  }
};

// 暴露方法给父组件
defineExpose({ accommodationDetailSub, accommodationDetailTempSave, getSubmitData });

onMounted(() => {
  nextTick(() => {
    // 如果没有数据，自动添加一条
    if (!props.accommodationDetailList || props.accommodationDetailList.length === 0) {
      handleAddAccommodationDetail();
      isInitialized.value = true;
    }
  });
  // 监听签到人数变化，自动更新住宿详单中的签到人数
  watch(
    () => props.realSignInPersonNum,
    (newSignInNum) => {
      if (props.accommodationDetailList.length > 0) {
        const updatedList = props.accommodationDetailList.map(item => ({
          ...item,
          checkInPersonNum: newSignInNum
        }));
        emit('accommodationDetailEmit', updatedList);

        // 重新计算比对结果
        updatedList.forEach(item => {
          updateComparisonResult(item.tempId);
        });
      }
    }
  );
  // 监听 accommodationDetailList 变化
  watch(
    () => props.accommodationDetailList,
    (newList) => {
      if (newList && newList.length > 0 && !isInitialized.value) {
        // 检查是否需要处理附件回显
        const needsProcessing = newList.some(
          (item) => item.paths && item.paths.length > 0 || (!item?._attachments && item?._attachments.length > 0),
        );

        if (needsProcessing) {
          initAccommodationDetailFromCache(newList);
        } else {
          isInitialized.value = true;
        }
      }
    },
    { immediate: true, deep: true },
  );

  // 🔥 新增：监听 schemeDetail 变化（仅查看模式使用）
  watch(() => props.schemeDetail, (newSchemeDetail) => {
    // 只在查看模式下处理 schemeDetail
    if (props.readonly && newSchemeDetail && newSchemeDetail.attachmentStayChecks) {
      // 查看模式：直接使用数据展示，通过 emit 传递给父组件进行显示
      emit('accommodationDetailEmit', newSchemeDetail.attachmentStayChecks);
    }
  }, { immediate: true, deep: true });

});
</script>

<template>
  <!-- 住宿详单 -->
  <div class="scheme_accommodation_detail">
    <div class="interact_title">
      <div class="interact_shu mr20"></div>
      <span>住宿详单</span>
    </div>

    <!-- 可编辑表格 -->
    <div class="info-table-wrapper accommodation-table" id="accommodationTable">
      <div class="table-header">
        <div class="col-serial font-color">序号</div>
        <div class="col-attachment font-color">附件</div>
        <div class="col-checkinperson font-color">签到人数</div>
        <div class="col-detailperson font-color">详单人数</div>
        <div class="col-comparison font-color">比对结果</div>
        <div class="col-actions font-color">操作</div>
      </div>

      <div class="table-body">
        <div v-for="(item, index) in (readonly ? displayAccommodationDetailList : props.accommodationDetailList)" :key="item.tempId || item.id || index"
          class="table-row" :id="'accommodation'+item.serialNumber">
          <!-- 序号 -->
          <div class="col-serial">
            {{ item.serialNumber }}
          </div>

          <!-- 附件 -->
          <div class="col-attachment">
            <div class="attachment-content">
              <!-- 已上传文件标签 -->
              <div class="file-tags" v-if="item._attachments && item._attachments.length > 0">
                <a-tag v-for="file in item._attachments" :key="file.uid" :closable="!readonly" class="file-tag"
                  @click="() => handlePreviewFile(file)" @close="() => handleRemoveFile(file, item.tempId)">
                  {{ getFileDisplayName(file.name) }}
                </a-tag>
              </div>
              <div v-else-if="readonly" class="readonly-text">暂无附件</div>

              <!-- 上传按钮 -->
              <a-upload :file-list="[]" :custom-request="(options: any) => uploadRequest(options, item.tempId)"
                :multiple="true" :show-upload-list="false" :accept="UPLOAD_ACCEPT">
                <a-button size="small" type="link"
                  :loading="item._uploading || itemUploadingState[item.tempId] || false">
                  <upload-outlined />
                  上传
                </a-button>
              </a-upload>
            </div>
          </div>


          <!-- 签到人数 - 始终为只读显示 -->
          <div class="col-checkinperson">
            <span class="readonly-text readonly-highlight">
              {{ item.checkInPersonNum }}
            </span>
          </div>

          <!-- 详单人数 - 始终可编辑 -->
          <div class="col-detailperson">
            <a-input-number v-model:value="item.detailPersonNum" :min="0" placeholder="详单人数" size="small"
              class="borderless-input" :bordered="false" :max="999999"
              @change="(value: number | null) => updateAccommodationDetail(item.tempId, 'detailPersonNum', value)"
              @blur="() => updateComparisonResult(item.tempId)" />
          </div>

          <!-- 比对结果 -->
          <div class="col-comparison">
            <span :class="item.comparisonResult === 1 ? 'result-consistent' : 'result-inconsistent'">
              {{ item.comparisonResult === 1 ? '一致' : '不一致' }}
            </span>
          </div>

          <!-- 操作 -->
          <div class="col-actions">
            <a-button type="link" danger size="small" @click="() => handleDeleteItem(item.tempId)">
              <img :src="arrow" alt="" class="imgBig">
            </a-button>
          </div>
        </div>

        <!-- 添加按钮行 - 当没有数据时显示 -->
        <div v-if="props.accommodationDetailList.length === 0" class="table-row add-row">
          <div class="add-button-full-width" @click="handleAddAccommodationDetail">
            <div class="demand_add">
              <img :src="add" alt="" class="imgAddBig" style="margin-right: 5px;">
              <span>新增住宿详单</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 文件预览弹框 -->
    <a-modal v-model:open="previewVisible" title="文件预览" :footer="null" width="80%" @cancel="handlePreviewCancel">
      <div class="preview-content">
        <div class="preview-header">
          <h4>{{ previewFileName }}</h4>
        </div>
        <div class="preview-body">
          <template v-if="previewFile && previewFile.url">
            <!-- 图片预览 -->
            <img v-if="
              previewFile.name &&
              (previewFile.name.toLowerCase().includes('.jpg') ||
                previewFile.name.toLowerCase().includes('.jpeg') ||
                previewFile.name.toLowerCase().includes('.png') ||
                previewFile.name.toLowerCase().includes('.gif'))
            " :src="previewFile.url" alt="预览图片" style="max-width: 100%; max-height: 500px; object-fit: contain" />
            <!-- PDF预览 -->
            <iframe v-else-if="previewFile.name && previewFile.name.toLowerCase().includes('.pdf')"
              :src="previewFile.url" style="width: 100%; height: 500px; border: none"></iframe>
            <!-- 其他文件类型显示下载链接 -->
            <div v-else class="file-download">
              <p>无法预览此文件类型，请下载查看</p>
              <a-button type="primary" @click="handleDownloadFile"> 下载文件 </a-button>
            </div>
          </template>
          <template v-else>
            <div class="no-file">
              <p>文件信息：{{ previewFileName }}</p>
              <p v-if="previewFile && previewFile.status === 'uploading'">文件正在上传中，请稍候...</p>
              <p v-else>暂无可预览的文件内容</p>
            </div>
          </template>
        </div>
      </div>
    </a-modal>

  </div>
</template>

<style scoped lang="less">
.font-color {
  color: #86909C;
}

.imgBig {
  width: 20px;
  height: 20px;
}

.imgAddBig {
  width: 16px;
  height: 16px;
}

.scheme_accommodation_detail {
  .interact_title {
    display: flex;
    align-items: center;
    margin-bottom: 16px;
    font-size: 18px;
    font-weight: 500;

    .interact_shu {
      width: 4px;
      height: 20px;
      background: #1868db;
      border-radius: 2px;
    }

    span {
      font-size: 18px;
      color: #1d2129;
      font-family: PingFangSC, PingFang SC;
    }

    .tip-text {
      margin-left: 16px;
      font-size: 12px;
      color: #ff4d4f;
      font-weight: normal;
    }
  }

  .info-table-wrapper {
    width: 100%;
    border: none;
    border-radius: 0;
    margin-bottom: 0;

    &.accommodation-table {
      width: 100%;
    }

    .table-header {
      display: flex;
      background-color: #f2f3f5;
      font-weight: 500;
      font-size: 14px;
      color: #333;

      >div {
        padding: 12px 8px;
        text-align: center;
      }

      .col-serial {
        width: 80px;
        // border-right: 1px solid #d9d9d9;
      }

      .col-attachment {
        width: 300px;
        // border-right: 1px solid #d9d9d9;
      }

      .col-checkinperson {
        width: 150px;
        // border-right: 1px solid #d9d9d9;
      }

      .col-detailperson {
        width: 150px;
        // border-right: 1px solid #d9d9d9;
      }

      .col-comparison {
        width: 100px;
        // border-right: 1px solid #d9d9d9;
      }

      .col-actions {
        width: 80px;
      }
    }

    .table-body {
      .table-row {
        display: flex;
        border-bottom: 1px solid #e5e6eb;

        &:last-child {
          border-bottom: none;
        }

        &.add-row {
          border-bottom: none;

          .add-button-full-width {
            width: 100%;
            padding: 8px 12px;
            display: flex;
            align-items: center;
            justify-content: flex-start;
            min-height: 38px;
            cursor: pointer;
            border-bottom: none;

            &:hover {
              background-color: #f5f5f5;
            }

            .demand_add {
              display: flex;
              align-items: center;
              justify-content: flex-start;
              color: #1890ff;
              font-size: 14px;
              margin-left: 8px;

              .demand_add_img {
                width: 16px;
                height: 16px;
                background: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTggMVY4TTE1IDhIOE04IDE1VjhNMSA4SDgiIHN0cm9rZT0iIzE4OTBGRiIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiLz4KPC9zdmc+') no-repeat center;
                background-size: contain;
                margin-right: 8px;
              }
            }
          }
        }

        >div {
          padding: 12px 8px;
          display: flex;
          align-items: center;
          justify-content: center;
          min-height: 38px;
          // border-right: 1px solid #f0f0f0;

          &:last-child {
            border-right: none;
          }
        }

        .col-serial {
          width: 80px;
        }

        .col-attachment {
          width: 300px;
          flex-direction: row;
          align-items: center;
          justify-content: flex-start;
          gap: 4px;

          .attachment-content {
            width: 100%;
            display: flex;
            flex-direction: row;
            align-items: center;
            gap: 4px;
            flex-wrap: wrap;
            justify-content: flex-start;

            .file-tags {
              display: flex;
              flex-wrap: wrap;
              gap: 4px;
              justify-content: flex-start;
              max-width: 100%;

              .file-tag {
                cursor: pointer;
                font-size: 12px;
                background-color: #e6f7ff;
                border-color: #1890ff;
                color: #1890ff;

                &:hover {
                  opacity: 0.8;
                  background-color: #bae7ff;
                }
              }
            }
          }
        }

        .col-checkinperson {
          width: 150px;
        }

        .col-detailperson {
          width: 150px;
        }

        .col-comparison {
          width: 100px;
        }

        .col-actions {
          width: 80px;
          display: flex;
          justify-content: center;
        }
      }
    }
  }

  // 无边框输入框样式
  .borderless-input {
    border: none !important;
    box-shadow: none !important;
    background: transparent !important;

    &:focus,
    &:hover {
      border: none !important;
      box-shadow: none !important;
    }

    .ant-input {
      border: none !important;
      box-shadow: none !important;
      background: transparent !important;
      text-align: center !important; // 输入框内容居中
    }

    .ant-input-number-input {
      text-align: center !important; // 数字输入框内容居中
    }

    .ant-picker-input>input {
      border: none !important;
      box-shadow: none !important;
      text-align: center !important; // 日期选择器输入框内容居中
    }
  }

  .preview-content {
    .preview-header {
      margin-bottom: 16px;
      padding-bottom: 8px;
      border-bottom: 1px solid #f0f0f0;

      h4 {
        margin: 0;
        font-size: 16px;
        color: #333;
      }
    }

    .preview-body {
      text-align: center;

      .file-download {
        padding: 40px 0;

        p {
          margin-bottom: 16px;
          color: #666;
        }
      }

      .no-file {
        padding: 40px 0;
        color: #999;

        p {
          margin: 8px 0;
        }
      }
    }
  }

}

.mr8 {
  margin-right: 8px;
}

.mr20 {
  margin-right: 20px;
}

// 全局样式覆盖
:deep(.ant-input-number) {
  width: 100%;

  .ant-input-number-input {
    text-align: center !important; // 确保数字输入框内容居中
  }
}

:deep(.ant-select) {
  width: 100%;
}

:deep(.ant-picker) {
  width: 100%;
}

// 查看模式纯文本样式
.readonly-text {
  color: #333;
  font-size: 14px;
  line-height: 1.5;
  padding: 4px 0;
  display: inline-block;
  width: 100%;
  text-align: center;

  &.small {
    font-size: 12px;
    color: #999;
  }

  // 只读高亮样式，用于标识从父组件传入的值
  &.readonly-highlight {
    font-weight: 500;
    border-radius: 4px;
    padding: 4px 8px;
  }
}

// 比对结果样式
.result-consistent {
  color: #1890ff;
  font-weight: 500;
}

.result-inconsistent {
  color: #ff4d4f;
  font-weight: 500;
}
</style>
