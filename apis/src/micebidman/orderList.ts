import { MiceBidManOrderList, PlatformCount, ListRes, HotelSatisfyListRes, Details_3Res, MerchantListRes, QueryPoolIdsRes, DetailsRes, Details_1Res, ReceiveRejectParams, Count_3Res, IPageResponse, OneEndedMeeting } from '@haierbusiness-front/common-libs';
import { download, get, post, originalPost } from '../request';

export const miceBidManOrderListApi = {
  orderList: (params = {}): Promise<MiceBidManOrderList> => {
    return get('/mice-bid/api/mice/main/platform/page', params);
  },
  merchantOrderList: (params = {}): Promise<void> => {
    return get('/mice-bid/api/mice/main/merchant/page', params);
  },
  platformCount: (params = {}): Promise<PlatformCount> => {
    return get('/mice-bid/api/mice/main/platform/count', params);
  },
  merchantCount: (params = {}): Promise<PlatformCount> => {
    return get('/mice-bid/api/mice/main/merchant/count', params);
  },
  orderListU: (params = {}): Promise<IPageResponse<MiceBidManOrderList>> => {
    return get('/mice-bid/api/mice/main/user/page', params);
  },
  //
  active: (params = {}): Promise<IPageResponse<MiceBidManOrderList>> => {
    return get('/mice-bid/api/mice/main/platform/list/active', params);
  },
  //查询待评价
  evaluated: (params = {}): Promise<OneEndedMeeting> => {
    return get('/mice-bid/api/mice/main/user/one/ended/meeting', params);
  },
  platformCountU: (params = {}): Promise<PlatformCount> => {
    return get('/mice-bid/api/mice/main/user/count', params);
  },
  metaList: (params = {}): Promise<void> => {
    return post('/mice-bid/api/framework/define/meta/list', params);
  },
  publish: (params = {}): Promise<ListRes> => {
    return originalPost('/mice-bid/api/mice/demand/platform/push', params);
  },
  // 用户礼品确认
  infoConfirm: (params = {}): Promise<ListRes> => {
    return originalPost('/mice-bid/api/mice/scheme/user/present/info/confirm', params);
  },
  // 用户礼品确认
  userConfirm: (params = {}): Promise<ListRes> => {
    return originalPost('/mice-bid/api/mice/bill/user/confirm', params);
  },
  // 用户礼品确认
  userApprove: (params = {}): Promise<ListRes> => {
    return originalPost('/mice-bid/api/mice/bill/platform/re/approve', params);
  },
  processDetails: (params = {}): Promise<Details_3Res> => {
    return get('/mice-bid/api/framework/process/details', params);
  },
  queryPoolIds: (params = {}): Promise<QueryPoolIdsRes> => {
    return get('/mice-bid/api/mice/demand/platform/query/poolIds', params);
  },
  specialHotelWithPool: (params = {}): Promise<void> => {
    return get('/mice-bid/api/mice/demand/platform/specialHotelWithPool', params);
  },
  merchantList: (params = {}): Promise<MerchantListRes> => {
    return get('/mice-bid/api/mice/demand/platform/merchant/list', params);
  },
  userDetails: (params = {}): Promise<DetailsRes> => {
    return get('/mice-bid/api/mice/demand/user/details', params);
  },
  billPlatformDetails: (params = {}): Promise<DetailsRes> => {
    return get('/mice-bid/api/mice/bill/platform/details', params);
  },

  platformDetails: (params = {}): Promise<Details_1Res> => {
    return get('/mice-bid/api/mice/demand/platform/details', params);
  },
  hotelsDetails: (params = {}): Promise<Details_1Res> => {
    return get('/mice-bid/api/mice/demand/platform/push/hotels/details', params);
  },
  groupDetails: (params = {}): Promise<Details_1Res> => {
    return get('/mice-bid/api/mice/demand/platform/push/merchants/group/details', params);
  },
  //节点详情
  nodeDetails: (params = {}): Promise<Details_1Res> => {
    return get('/mice-bid/api/mice/main/platform/process/info', params);
  },
  satisfyList: (params = {}): Promise<HotelSatisfyListRes> => {
    return get('/mice-bid/api/mice/demand/platform/hotel/satisfy/list', params);
  },
  receive_reject: (params = {}): Promise<ReceiveRejectParams> => {
    return originalPost('/mice-bid/api/mice/demand/platform/receive_reject', params);
  },
  addIntention: (params = {}): Promise<ReceiveRejectParams> => {
    return originalPost('/mice-bid/api/counsellor/user/add-intention', params);
  },
  submitFinish: (params = {}): Promise<ReceiveRejectParams> => {
    return originalPost('/mice-bid/api/mice/scheme/platform/submit/finish', params);
  },
  counsellorUserCount: (params = {}): Promise<Count_3Res> => {
    return get('/mice-bid/api/counsellor/user/count', params);
  },
  userAssign: (params = {}): Promise<ListRes> => {
    return originalPost('/mice-bid/api/mice/demand/platform/receive', params);
  },
  districtTree: (params = {}): Promise<void> => {
    return get('/common/api/district/trees', params);
  },
  recordPage: (params = {}): Promise<void> => {
    return get('/mice-bid/api/common/approve/record/page', params);
  },
  changeRecord: (params = {}): Promise<void> => {
    return get('/mice-bid/api/mice/scheme/merchant/change/record', params);
  },
  // 特殊权限
  powerSpecial: (params = {}): Promise<void> => {
    return get('/mice-bid/api/mice/special/power/special', params);
  },
  // 开通特殊权限
  powerOpen: (params = {}): Promise<void> => {
    return originalPost('/mice-bid/api/mice/special/power/open', params);
  },
  recordRevoke: (params = {}): Promise<void> => {
    return get('/mice-bid/api/common/approve/record/revoke', params);
  },
  cancelPush: (params = {}): Promise<ListRes> => {
    return originalPost('/mice-bid/api/mice/demand/platform/cancel/push', params);
  },
  bidSwitch: (params = {}): Promise<ListRes> => {
    return originalPost('/mice-bid/api/mice/scheme/platform/scheme/bid/switch', params);
  },


  //** 发票上传 */
  invoiceSubmit: (params = {}): Promise<ReceiveRejectParams> => {
    return originalPost('/mice-bid/api/mice/balance/plat/invoice/submit', params);
  },
  // 上传退款凭证
  refundVoucher: (params = {}): Promise<ReceiveRejectParams> => {
    return originalPost('/mice-bid/api/mice/balance/refund/upload/voucher', params);
  },
  // 上传支付凭证
  paymentVoucher: (params = {}): Promise<ReceiveRejectParams> => {
    return originalPost('/mice-bid/api/mice/balance/payment/upload/voucher', params);
  },

};
